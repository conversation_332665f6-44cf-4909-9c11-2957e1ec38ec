@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Header specific styles */
.header-dropdown {
  transition: all 0.2s ease-in-out;
}

/* Form styles */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* Button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors;
}

/* Prose styles for content pages */
.prose {
  @apply text-gray-700;
}

.prose h2 {
  @apply text-2xl font-bold text-gray-900 mt-8 mb-4;
}

.prose h3 {
  @apply text-xl font-semibold text-gray-900 mt-6 mb-3;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside mb-4 space-y-2;
}

.prose li {
  @apply text-gray-700;
}

/* Enhanced Lighthouse Animation Styles */
.lighthouse-container {
  position: relative;
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.light-beam {
  animation: lighthouse-sweep 6s linear infinite;
  transform-origin: 200px 117px;
}

@keyframes lighthouse-sweep {
  0% {
    transform: rotate(-60deg);
    opacity: 0.1;
  }
  10% {
    opacity: 0.4;
  }
  20% {
    transform: rotate(-30deg);
    opacity: 0.7;
  }
  30% {
    opacity: 0.9;
  }
  40% {
    transform: rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: rotate(30deg);
    opacity: 0.9;
  }
  60% {
    opacity: 0.7;
  }
  70% {
    transform: rotate(60deg);
    opacity: 0.4;
  }
  80% {
    opacity: 0.1;
  }
  100% {
    transform: rotate(-60deg);
    opacity: 0.1;
  }
}

/* Enhanced Light source pulsing */
.light-source {
  animation: light-pulse 2.5s ease-in-out infinite;
}

.light-source-inner {
  animation: light-pulse-inner 1.8s ease-in-out infinite;
}

@keyframes light-pulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.15);
  }
}

@keyframes light-pulse-inner {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.3);
  }
}

/* Enhanced Water wave animation */
.water-waves {
  animation: wave-motion 4s ease-in-out infinite;
}

@keyframes wave-motion {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
  }
  25% {
    transform: translateX(3px) translateY(-1px);
  }
  50% {
    transform: translateX(6px) translateY(0px);
  }
  75% {
    transform: translateX(3px) translateY(1px);
  }
}

/* Enhanced Cloud animations */
.cloud-1 {
  animation: cloud-drift-1 12s ease-in-out infinite;
}

.cloud-2 {
  animation: cloud-drift-2 15s ease-in-out infinite;
}

@keyframes cloud-drift-1 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0.7;
  }
  33% {
    transform: translateX(8px) translateY(-2px);
    opacity: 0.5;
  }
  66% {
    transform: translateX(15px) translateY(1px);
    opacity: 0.3;
  }
}

@keyframes cloud-drift-2 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
    opacity: 0.6;
  }
  33% {
    transform: translateX(-10px) translateY(2px);
    opacity: 0.4;
  }
  66% {
    transform: translateX(-18px) translateY(-1px);
    opacity: 0.2;
  }
}

/* Bird animations */
.bird-1 {
  animation: bird-fly-1 8s ease-in-out infinite;
}

.bird-2 {
  animation: bird-fly-2 10s ease-in-out infinite;
}

.bird-3 {
  animation: bird-fly-3 12s ease-in-out infinite;
}

@keyframes bird-fly-1 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
  }
  50% {
    transform: translateX(20px) translateY(-10px);
  }
}

@keyframes bird-fly-2 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
  }
  50% {
    transform: translateX(-15px) translateY(8px);
  }
}

@keyframes bird-fly-3 {
  0%, 100% {
    transform: translateX(0px) translateY(0px);
  }
  50% {
    transform: translateX(25px) translateY(-5px);
  }
}

/* Reflection animation */
.reflection {
  animation: reflection-shimmer 3s ease-in-out infinite;
}

@keyframes reflection-shimmer {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.1;
    transform: scaleY(0.8);
  }
}

/* Enhanced lighthouse effects */
.lighthouse {
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15)) drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
}

/* Responsive lighthouse sizing */
@media (max-width: 1024px) {
  .lighthouse-container svg {
    width: 360px;
    height: 420px;
  }
}

@media (max-width: 768px) {
  .lighthouse-container svg {
    width: 320px;
    height: 380px;
  }
}

@media (max-width: 640px) {
  .lighthouse-container svg {
    width: 280px;
    height: 340px;
  }
}

@media (max-width: 480px) {
  .lighthouse-container svg {
    width: 240px;
    height: 300px;
  }
}
