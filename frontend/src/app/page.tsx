'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in
    const token = document.cookie.split(';').find(cookie => cookie.trim().startsWith('token='));
    if (token) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative w-full overflow-x-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg width="60" height="60" viewBox="0 0 60 60" className="absolute inset-0 h-full w-full">
          <defs>
            <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
              <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#1e40af" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden z-10 w-full">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative z-10 pb-8 sm:pb-16 md:pb-20 lg:pb-28 xl:pb-32 w-full">
            <main className="mt-10 w-full max-w-7xl mx-auto px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                {/* Left side - Text content */}
                <div className="text-left">
                  <h1 className="text-4xl tracking-tight font-bold text-gray-900 sm:text-5xl md:text-6xl">
                    <span className="block text-blue-600">Optimizing</span>
                    <span className="block">Maritime's Last Mile</span>
                  </h1>
                  <p className="mt-6 text-base text-gray-600 sm:text-lg sm:max-w-xl md:mt-8 md:text-xl">
                    AI-powered, End-to-End Port Cost Management Software Solution
                  </p>
                  <p className="mt-4 text-sm text-gray-500 sm:text-base">
                    Seamless Disbursement Account Analysis Across Every Port Call.
                    Our comprehensive platform streamlines maritime operations, reduces costs,
                    and provides accurate calculations for all your port service needs including
                    pilotage, tugboat services, port dues, and agency fees.
                  </p>

                  {/* CTA Buttons */}
                  <div className="mt-8 sm:mt-10 sm:flex sm:justify-start">
                    <div className="rounded-md shadow">
                      <Link
                        href="/register"
                        className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10 transition-all duration-200"
                      >
                        Get Started
                      </Link>
                    </div>
                    <div className="mt-3 sm:mt-0 sm:ml-3">
                      <Link
                        href="/login"
                        className="w-full flex items-center justify-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10 transition-all duration-200"
                      >
                        Sign In
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Right side - Lighthouse with animation */}
                <div className="relative flex justify-center lg:justify-end w-full">
                  <div className="relative">
                    {/* Background elements */}
                    <div className="absolute inset-0 bg-gradient-to-b from-blue-100 to-blue-200 rounded-full opacity-20 blur-3xl"></div>

                    {/* Lighthouse SVG */}
                    <div className="lighthouse-container relative z-10">
                      <svg
                        width="350"
                        height="450"
                        viewBox="0 0 350 450"
                        className="lighthouse"
                      >
                        {/* Enhanced Gradient definitions */}
                        <defs>
                          <linearGradient id="lightGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#FFF700" stopOpacity="0.95" />
                            <stop offset="20%" stopColor="#FFE135" stopOpacity="0.8" />
                            <stop offset="40%" stopColor="#FFD700" stopOpacity="0.6" />
                            <stop offset="70%" stopColor="#FFA500" stopOpacity="0.4" />
                            <stop offset="100%" stopColor="#FF8C00" stopOpacity="0.1" />
                          </linearGradient>
                          <linearGradient id="towerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#E8E8E8" />
                            <stop offset="30%" stopColor="#F5F5F5" />
                            <stop offset="50%" stopColor="#FFFFFF" />
                            <stop offset="70%" stopColor="#F0F0F0" />
                            <stop offset="100%" stopColor="#D8D8D8" />
                          </linearGradient>
                          <linearGradient id="towerShadow" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#C0C0C0" />
                            <stop offset="50%" stopColor="#E0E0E0" />
                            <stop offset="100%" stopColor="#A0A0A0" />
                          </linearGradient>
                          <radialGradient id="lightSource" cx="50%" cy="50%" r="50%">
                            <stop offset="0%" stopColor="#FFFF88" stopOpacity="1" />
                            <stop offset="30%" stopColor="#FFFF00" stopOpacity="0.9" />
                            <stop offset="70%" stopColor="#FFD700" stopOpacity="0.7" />
                            <stop offset="100%" stopColor="#FFA500" stopOpacity="0.3" />
                          </radialGradient>
                          <linearGradient id="baseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#8B4513" />
                            <stop offset="50%" stopColor="#A0522D" />
                            <stop offset="100%" stopColor="#654321" />
                          </linearGradient>
                          <linearGradient id="roofGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#2D3748" />
                            <stop offset="50%" stopColor="#4A5568" />
                            <stop offset="100%" stopColor="#1A202C" />
                          </linearGradient>
                          <linearGradient id="waterGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#4299E1" />
                            <stop offset="50%" stopColor="#3182CE" />
                            <stop offset="100%" stopColor="#2B6CB0" />
                          </linearGradient>
                        </defs>

                        {/* Enhanced Water waves - animated */}
                        <g className="water-waves">
                          <path d="M 0 420 Q 87 410 175 420 T 350 420 L 350 450 L 0 450 Z" fill="url(#waterGradient)" opacity="0.8" />
                          <path d="M 0 430 Q 87 425 175 430 T 350 430 L 350 450 L 0 450 Z" fill="#3182CE" opacity="0.6" />
                          <path d="M 0 425 Q 58 415 116 425 T 232 425 T 350 425 L 350 450 L 0 450 Z" fill="#2B6CB0" opacity="0.4" />
                          <path d="M 0 435 Q 70 430 140 435 T 280 435 T 350 435 L 350 450 L 0 450 Z" fill="#1E40AF" opacity="0.3" />
                        </g>

                        {/* Rocky foundation */}
                        <ellipse cx="175" cy="415" rx="45" ry="8" fill="#4A5568" opacity="0.6" />
                        <ellipse cx="175" cy="410" rx="40" ry="6" fill="#2D3748" opacity="0.4" />

                        {/* Enhanced Lighthouse base with stone texture */}
                        <rect x="140" y="390" width="70" height="30" fill="url(#baseGradient)" stroke="#654321" strokeWidth="2" />
                        <rect x="135" y="385" width="80" height="12" fill="#A0522D" stroke="#8B4513" strokeWidth="1" />

                        {/* Base details */}
                        <rect x="145" y="395" width="60" height="3" fill="#654321" opacity="0.6" />
                        <rect x="145" y="405" width="60" height="3" fill="#654321" opacity="0.6" />
                        <rect x="145" y="415" width="60" height="3" fill="#654321" opacity="0.6" />

                        {/* Enhanced Lighthouse tower with realistic proportions */}
                        <polygon points="150,390 200,390 190,120 160,120" fill="url(#towerGradient)" stroke="#C0C0C0" strokeWidth="2" />

                        {/* Tower shadow side */}
                        <polygon points="190,120 200,390 195,390 185,120" fill="url(#towerShadow)" opacity="0.3" />

                        {/* Enhanced red stripes with 3D effect */}
                        <rect x="150" y="170" width="50" height="25" fill="#DC2626" stroke="#B91C1C" strokeWidth="1" />
                        <rect x="195" y="170" width="5" height="25" fill="#991B1B" />

                        <rect x="150" y="230" width="50" height="25" fill="#DC2626" stroke="#B91C1C" strokeWidth="1" />
                        <rect x="195" y="230" width="5" height="25" fill="#991B1B" />

                        <rect x="150" y="290" width="50" height="25" fill="#DC2626" stroke="#B91C1C" strokeWidth="1" />
                        <rect x="195" y="290" width="5" height="25" fill="#991B1B" />

                        <rect x="150" y="350" width="50" height="25" fill="#DC2626" stroke="#B91C1C" strokeWidth="1" />
                        <rect x="195" y="350" width="5" height="25" fill="#991B1B" />

                        {/* Windows */}
                        <rect x="165" y="200" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />
                        <rect x="177" y="200" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />
                        <rect x="165" y="260" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />
                        <rect x="177" y="260" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />
                        <rect x="165" y="320" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />
                        <rect x="177" y="320" width="8" height="12" fill="#1E40AF" stroke="#1E3A8A" strokeWidth="1" rx="2" />

                        {/* Enhanced lighthouse top structure */}
                        <rect x="145" y="100" width="60" height="35" fill="url(#roofGradient)" stroke="#1A202C" strokeWidth="2" />
                        <polygon points="145,100 205,100 190,75 160,75" fill="#1A202C" stroke="#000000" strokeWidth="1" />

                        {/* Lantern room details */}
                        <rect x="150" y="105" width="50" height="25" fill="#2D3748" stroke="#1A202C" strokeWidth="1" />
                        <rect x="155" y="110" width="40" height="15" fill="#4A5568" opacity="0.8" />

                        {/* Enhanced Light source */}
                        <circle cx="175" cy="117" r="12" fill="url(#lightSource)" className="light-source" />
                        <circle cx="175" cy="117" r="8" fill="#FFFF00" opacity="0.9" className="light-source-inner" />
                        <circle cx="175" cy="117" r="4" fill="#FFFFFF" opacity="0.7" />

                        {/* Enhanced Light beam - animated */}
                        <g className="light-beam">
                          <path
                            d="M 175 110 L 320 60 L 335 85 L 330 110 L 335 135 L 320 160 L 175 125 Z"
                            fill="url(#lightGradient)"
                            opacity="0.9"
                          />
                          <path
                            d="M 175 115 L 310 75 L 320 100 L 315 125 L 175 120 Z"
                            fill="#FFFF00"
                            opacity="0.6"
                          />
                          <path
                            d="M 175 117 L 300 90 L 305 110 L 175 118 Z"
                            fill="#FFFFFF"
                            opacity="0.4"
                          />
                        </g>

                        {/* Enhanced atmospheric effects */}
                        <g className="clouds">
                          <ellipse cx="90" cy="140" rx="20" ry="12" fill="#FFFFFF" opacity="0.7" className="cloud cloud-1" />
                          <ellipse cx="105" cy="135" rx="15" ry="10" fill="#FFFFFF" opacity="0.6" className="cloud cloud-1" />
                          <ellipse cx="80" cy="145" rx="12" ry="8" fill="#FFFFFF" opacity="0.5" className="cloud cloud-1" />

                          <ellipse cx="260" cy="160" rx="22" ry="14" fill="#FFFFFF" opacity="0.6" className="cloud cloud-2" />
                          <ellipse cx="280" cy="155" rx="18" ry="12" fill="#FFFFFF" opacity="0.5" className="cloud cloud-2" />
                          <ellipse cx="245" cy="165" rx="15" ry="10" fill="#FFFFFF" opacity="0.4" className="cloud cloud-2" />
                        </g>

                        {/* Birds in the distance */}
                        <g className="birds" opacity="0.4">
                          <path d="M 50 100 Q 55 95 60 100 Q 55 105 50 100" fill="#2D3748" className="bird bird-1" />
                          <path d="M 70 110 Q 75 105 80 110 Q 75 115 70 110" fill="#2D3748" className="bird bird-2" />
                          <path d="M 290 120 Q 295 115 300 120 Q 295 125 290 120" fill="#2D3748" className="bird bird-3" />
                        </g>

                        {/* Lighthouse reflection in water */}
                        <g className="reflection" opacity="0.3">
                          <polygon points="150,420 200,420 190,450 160,450" fill="url(#towerGradient)" />
                          <rect x="150" y="430" width="50" height="8" fill="#DC2626" opacity="0.6" />
                          <circle cx="175" cy="425" r="4" fill="#FFD700" opacity="0.8" />
                        </g>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Comprehensive Maritime Solutions
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Everything you need to manage your maritime operations efficiently
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {/* Feature 1 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-blue-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Maritime Calculations
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Accurate proforma calculations for all maritime services including pilotage,
                    tugboat, and port dues with real-time tariff updates.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-green-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Cost Management
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Track and manage all your maritime costs with detailed breakdowns,
                    historical data, and predictive analytics.
                  </p>
                </div>
              </div>

              {/* Feature 3 */}
              <div className="relative group">
                <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-center justify-center h-16 w-16 rounded-lg bg-purple-500 text-white mx-auto mb-6">
                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 text-center mb-4">
                    Professional Reports
                  </h3>
                  <p className="text-gray-600 text-center leading-relaxed">
                    Generate professional proforma reports and documentation
                    for your clients and stakeholders with PDF export.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-600">
            Professional maritime calculation software for shipping industry professionals.
          </p>
          <p className="mt-2 text-sm text-gray-500">
            Streamline your operations with our comprehensive port cost management solution.
          </p>
        </div>
      </div>
    </div>
  );
}
